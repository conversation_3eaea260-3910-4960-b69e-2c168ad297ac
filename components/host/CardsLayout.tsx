import { <PERSON>, CardBody, CardHeader } from "@heroui/card";
import { Button } from "@heroui/button";
import { motion } from "framer-motion";
import { title } from "@/components/primitives";
import { contentVariants, cardItemVariants } from "@/utils/animationVariants";

interface HostCardsLayoutProps {
  className?: string;
}

export const HostCardsLayout: React.FC<HostCardsLayoutProps> = ({
  className
}) => {
  return (
    <motion.section
      className={`flex flex-col gap-6 py-8 md:py-10 ipad-content-section ${className || ""}`}
      variants={contentVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div
        className="text-center mb-8"
        variants={cardItemVariants}
      >
        <h1 className={title()}>主持功能中心</h1>
        <p className="text-default-600 mt-4">这里是主持相关功能的展示区域</p>
      </motion.div>

      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 gap-6"
        variants={contentVariants}
      >
        {/* 主持卡片1 */}
        <motion.div variants={cardItemVariants}>
          <Card className="p-4">
            <CardHeader className="pb-0 pt-2 px-4 flex-col items-start">
              <h4 className="font-bold text-large">主持工具 1</h4>
              <small className="text-default-500">主持功能介绍</small>
            </CardHeader>
            <CardBody className="overflow-visible py-2">
              <p className="text-default-600 mb-4">
                这是主持页面的第一个功能工具，提供专业的主持辅助功能。
              </p>
              <Button color="primary" variant="solid">
                开始主持
              </Button>
            </CardBody>
          </Card>
        </motion.div>

        {/* 主持卡片2 */}
        <motion.div variants={cardItemVariants}>
          <Card className="p-4">
            <CardHeader className="pb-0 pt-2 px-4 flex-col items-start">
              <h4 className="font-bold text-large">主持工具 2</h4>
              <small className="text-default-500">主持功能介绍</small>
            </CardHeader>
            <CardBody className="overflow-visible py-2">
              <p className="text-default-600 mb-4">
                这是主持页面的第二个功能工具，提供高级的主持管理功能。
              </p>
              <Button color="warning" variant="solid">
                管理设置
              </Button>
            </CardBody>
          </Card>
        </motion.div>
      </motion.div>
    </motion.section>
  );
};
