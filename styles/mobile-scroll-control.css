/**
 * iPad移动端滚动控制样式
 * 专门解决iPad上的上下滚动问题
 */

/* iPad专用滚动控制 - 竖屏和横屏 */
@media (min-width: 768px) and (max-width: 1024px) {
  /* 主容器滚动控制 */
  .ipad-scroll-container {
    height: 100vh;
    height: 100dvh; /* 动态viewport高度，适配Safari地址栏变化 */
    overflow: hidden;
    touch-action: none; /* 禁用触摸滚动手势 */
    -webkit-overflow-scrolling: auto; /* 禁用iOS弹性滚动 */
    display: flex;
    flex-direction: column;
  }

  /* 内容区域高度控制 */
  .ipad-content-section {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 1rem 1.5rem; /* 减少iPad上的内边距 */
  }

  /* 确保motion元素在固定高度容器中正常工作 */
  .ipad-scroll-container .motion-element {
    height: 100%;
    overflow: hidden;
  }

  /* 卡片网格在iPad上的特殊处理 */
  .ipad-content-section .grid {
    height: 100%;
    align-content: center;
  }
}

/* iPad Pro横屏特殊处理 */
@media (min-width: 1024px) and (max-width: 1366px) and (orientation: landscape) {
  .ipad-scroll-container {
    height: 100vh;
    height: 100dvh;
    overflow: hidden;
    touch-action: none;
  }

  .ipad-content-section {
    padding: 0.75rem 1.5rem; /* iPad Pro横屏时进一步减少垂直内边距 */
  }
}

/* 防止在iPad上出现意外滚动的全局规则 */
@media (min-width: 768px) and (max-width: 1366px) {
  /* 禁用body在iPad上的滚动 */
  body.ipad-no-scroll {
    overflow: hidden;
    position: fixed;
    width: 100%;
    height: 100%;
  }

  /* 确保HeroUI组件在固定高度容器中正常工作 */
  .ipad-scroll-container [data-slot="base"] {
    height: 100%;
  }

  /* 动画过程中防止滚动 */
  .ipad-scroll-container .motion-element[data-framer-appear-id] {
    overflow: hidden;
  }
}

/* 高对比度模式下的iPad优化 */
@media (min-width: 768px) and (max-width: 1024px) and (prefers-contrast: high) {
  .ipad-scroll-container {
    border: 1px solid currentColor; /* 在高对比度模式下显示容器边界 */
  }
}

/* 减少动画偏好下的iPad处理 */
@media (min-width: 768px) and (max-width: 1024px) and (prefers-reduced-motion: reduce) {
  .ipad-scroll-container .motion-element {
    transition: none !important;
    animation: none !important;
  }
}
